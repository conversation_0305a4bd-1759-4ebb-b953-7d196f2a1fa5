"use client";

import { cn } from "@/lib/utils";
import { motion } from "motion/react";
import React from "react";

interface AnimatedShinyTextProps
  extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  className?: string;
  shimmerWidth?: number;
}

export const AnimatedShinyText = React.forwardRef<
  HTMLDivElement,
  AnimatedShinyTextProps
>(({ children, className, shimmerWidth = 100, ...props }, ref) => {
  return (
    <div
      ref={ref}
      className={cn(
        "mx-auto max-w-md text-neutral-600/70 dark:text-neutral-400/70",
        className,
      )}
      {...props}
    >
      <div
        className={cn(
          "mx-auto max-w-md text-neutral-600/70 dark:text-neutral-400/70",
          "animate-shimmer bg-clip-text bg-no-repeat [background-position:0_0] [background-size:var(--shimmer-width)_100%] [transition:background-position_1s_cubic-bezier(.6,.6,0,1)_infinite]",
          "bg-gradient-to-r from-transparent via-black/80 via-50% to-transparent dark:via-white/80",
        )}
        style={
          {
            "--shimmer-width": `${shimmerWidth}%`,
          } as React.CSSProperties
        }
      >
        {children}
      </div>
    </div>
  );
});

AnimatedShinyText.displayName = "AnimatedShinyText";
